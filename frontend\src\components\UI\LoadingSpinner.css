.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: var(--space-8);
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
}

.loading-spinner.medium {
  width: 24px;
  height: 24px;
}

.loading-spinner.large {
  width: 32px;
  height: 32px;
}

.spinner {
  width: 100%;
  height: 100%;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

.loading-message {
  margin-top: var(--space-4);
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
