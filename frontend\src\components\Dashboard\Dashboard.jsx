import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import WeatherSearch from '../Weather/WeatherSearch';
import WeatherCard from '../Weather/WeatherCard';
import FavoritesList from '../Weather/FavoritesList';
import LoadingSpinner from '../UI/LoadingSpinner';
import LoadingSkeleton from '../UI/LoadingSkeleton';
import GuestPrompt from '../UI/GuestPrompt';

import { weatherAPI, favoritesAPI } from '../../services/api';
import toast from 'react-hot-toast';
import './Dashboard.css';

const Dashboard = () => {
  const { user, isAuthenticated, isGuest } = useAuth();
  const [currentWeather, setCurrentWeather] = useState(null);
  const [favorites, setFavorites] = useState([]);
  const [favoritesWeather, setFavoritesWeather] = useState([]);
  const [loading, setLoading] = useState(false);
  const [favoritesLoading, setFavoritesLoading] = useState(false);
  const [showGuestPrompt, setShowGuestPrompt] = useState(false);

  // Load user's favorite cities on component mount (only if authenticated)
  useEffect(() => {
    if (isAuthenticated) {
      setFavoritesLoading(true);
      loadFavorites();
    } else {
      setFavoritesLoading(false);
    }
  }, [isAuthenticated]);

  // Load weather for favorites when favorites change
  useEffect(() => {
    if (favorites.length > 0) {
      loadFavoritesWeather();
    } else {
      setFavoritesWeather([]);
      setFavoritesLoading(false);
    }
  }, [favorites]);

  const loadFavorites = async () => {
    try {
      setFavoritesLoading(true);
      const response = await favoritesAPI.getFavorites();
      setFavorites(response.data.data);
    } catch (error) {
      console.error('Error loading favorites:', error);
      toast.error('Failed to load favorite cities');
    } finally {
      setFavoritesLoading(false);
    }
  };

  const loadFavoritesWeather = async () => {
    try {
      setFavoritesLoading(true);
      const weatherPromises = favorites.map(async (favorite) => {
        try {
          const response = await weatherAPI.getWeatherByCoordinates(
            favorite.coordinates.lat,
            favorite.coordinates.lon
          );
          return {
            ...response.data.data,
            favoriteId: favorite._id
          };
        } catch (error) {
          console.error(`Error loading weather for ${favorite.cityName}:`, error);
          return null;
        }
      });

      const weatherResults = await Promise.all(weatherPromises);
      setFavoritesWeather(weatherResults.filter(weather => weather !== null));
    } catch (error) {
      console.error('Error loading favorites weather:', error);
      toast.error('Failed to load weather for favorite cities');
    } finally {
      setFavoritesLoading(false);
    }
  };

  const handleCitySearch = async (cityName) => {
    try {
      setLoading(true);
      const response = await weatherAPI.getCurrentWeather(cityName);
      setCurrentWeather(response.data.data);
    } catch (error) {
      console.error('Error searching city:', error);
      const errorMessage = error.response?.data?.message || 'Failed to get weather data';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToFavorites = async (weatherData) => {
    if (!isAuthenticated) {
      setShowGuestPrompt(true);
      toast.error('Please sign up or log in to save favorite cities');
      return;
    }

    try {
      const favoriteData = {
        cityName: weatherData.city,
        country: weatherData.country,
        coordinates: weatherData.coordinates,
        timezone: weatherData.timezone
      };

      await favoritesAPI.addFavorite(favoriteData);
      toast.success(`${weatherData.city} added to favorites!`);
      loadFavorites(); // Reload favorites list
    } catch (error) {
      console.error('Error adding to favorites:', error);
      const errorMessage = error.response?.data?.message || 'Failed to add city to favorites';
      if (error.response?.data?.code === 'AUTH_REQUIRED') {
        setShowGuestPrompt(true);
      }
      toast.error(errorMessage);
    }
  };

  const handleRemoveFromFavorites = async (favoriteId) => {
    try {
      await favoritesAPI.removeFavorite(favoriteId);
      toast.success('City removed from favorites');
      loadFavorites(); // Reload favorites list
    } catch (error) {
      console.error('Error removing from favorites:', error);
      const errorMessage = error.response?.data?.message || 'Failed to remove city from favorites';
      toast.error(errorMessage);
    }
  };

  const isInFavorites = (cityName, country) => {
    return favorites.some(
      fav => fav.cityName.toLowerCase() === cityName.toLowerCase() && 
             fav.country.toLowerCase() === country.toLowerCase()
    );
  };

  return (
    <div className="dashboard">
      <div className="dashboard-container">
        <div className="dashboard-hero">
          <div className="hero-content">
            <h1 className="hero-title">
              {isAuthenticated ? (
                <>Welcome back, {user?.name}</>
              ) : (
                <>WeatherApp</>
              )}
            </h1>

            <p className="hero-subtitle">
              {isAuthenticated
                ? 'Your personal weather dashboard'
                : 'Get accurate weather information for any location'
              }
            </p>
          </div>
        </div>

        <div className="search-section">
          <div className="search-container">
            <WeatherSearch onSearch={handleCitySearch} loading={loading} />
          </div>
        </div>

        {currentWeather && (
          <div className="current-weather-section">
            <div className="section-header">
              <h2 className="section-title">
                <span className="title-icon">🌤️</span>
                Current Weather
              </h2>
            </div>
            <div className="weather-card-container">
              <WeatherCard
                weather={currentWeather}
                onAddToFavorites={handleAddToFavorites}
                onRemoveFromFavorites={handleRemoveFromFavorites}
                isInFavorites={isInFavorites(currentWeather.city, currentWeather.country)}
                favoriteId={favorites.find(
                  fav => fav.cityName.toLowerCase() === currentWeather.city.toLowerCase() &&
                         fav.country.toLowerCase() === currentWeather.country.toLowerCase()
                )?._id}
              />
            </div>
          </div>
        )}

        {isAuthenticated ? (
          <div className="favorites-section">
            <div className="section-header">
              <h2 className="section-title">
                <span className="title-icon">⭐</span>
                Your Favorite Cities
              </h2>
            </div>
            <div className="favorites-container">
              {favoritesLoading ? (
                <LoadingSkeleton type="compact-card" count={3} />
              ) : (
                <FavoritesList
                  favorites={favoritesWeather}
                  onRemoveFromFavorites={handleRemoveFromFavorites}
                  onRefresh={loadFavoritesWeather}
                />
              )}
            </div>
          </div>
        ) : (
          showGuestPrompt && (
            <div className="guest-section">
              <div className="guest-container">
                <GuestPrompt
                  title="Save Your Favorite Cities"
                  message="Create a free account to save cities and get quick access to their weather"
                  action="favorites"
                />
              </div>
            </div>
          )
        )}
      </div>
    </div>
  );
};

export default Dashboard;
