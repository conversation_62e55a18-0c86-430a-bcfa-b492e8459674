#!/bin/bash

# <PERSON><PERSON>ka Weather App Setup Script
echo "🌤️  Setting up Kritka Weather App..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js (v16 or higher) first."
    echo "Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js version 16 or higher is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

echo "📦 Installing backend dependencies..."
cd backend && npm install && cd ..

echo "📦 Installing frontend dependencies..."
cd frontend && npm install && cd ..

# Create environment files if they don't exist
echo "⚙️  Setting up environment files..."

if [ ! -f "backend/.env" ]; then
    cp backend/.env.example backend/.env
    echo "📝 Created backend/.env from template"
    echo "⚠️  Please edit backend/.env and add your:"
    echo "   - MongoDB connection string"
    echo "   - OpenWeatherMap API key"
    echo "   - JWT secret"
fi

if [ ! -f "frontend/.env" ]; then
    cp frontend/.env.example frontend/.env
    echo "📝 Created frontend/.env from template"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Get your OpenWeatherMap API key from: https://openweathermap.org/api"
echo "2. Edit backend/.env and add your API key and database connection"
echo "3. Make sure MongoDB is running (locally or use MongoDB Atlas)"
echo "4. Run 'npm run dev' to start the application"
echo ""
echo "🚀 To start the application:"
echo "   npm run dev"
echo ""
echo "📖 For detailed instructions, see README.md"
