.premium-loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  min-height: 200px;
}

.premium-loader-container.small {
  padding: 1rem;
  min-height: 100px;
}

.premium-loader-container.large {
  padding: 4rem;
  min-height: 300px;
}

/* Default Luxury Spinner */
.luxury-spinner {
  position: relative;
  width: 80px;
  height: 80px;
}

.spinner-ring {
  position: absolute;
  border-radius: 50%;
  border: 2px solid transparent;
  animation: spin 2s linear infinite;
}

.ring-outer {
  width: 80px;
  height: 80px;
  border-top: 2px solid var(--luxury-gold);
  border-right: 2px solid var(--luxury-purple);
  animation-duration: 2s;
}

.ring-middle {
  width: 60px;
  height: 60px;
  top: 10px;
  left: 10px;
  border-top: 2px solid var(--luxury-cyan);
  border-left: 2px solid var(--luxury-rose);
  animation-duration: 1.5s;
  animation-direction: reverse;
}

.ring-inner {
  width: 40px;
  height: 40px;
  top: 20px;
  left: 20px;
  border-bottom: 2px solid var(--luxury-gold);
  border-right: 2px solid var(--luxury-purple);
  animation-duration: 1s;
}

.spinner-core {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  background: var(--gradient-luxury);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.7; }
}

/* Weather Loader */
.weather-loader {
  position: relative;
  width: 100px;
  height: 80px;
}

.cloud {
  position: relative;
  width: 80px;
  height: 40px;
}

.cloud-part {
  position: absolute;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
  animation: float 3s ease-in-out infinite;
}

.cloud-part-1 {
  width: 50px;
  height: 30px;
  top: 10px;
  left: 0;
}

.cloud-part-2 {
  width: 40px;
  height: 25px;
  top: 5px;
  left: 25px;
  animation-delay: -1s;
}

.cloud-part-3 {
  width: 35px;
  height: 20px;
  top: 15px;
  right: 0;
  animation-delay: -2s;
}

.rain {
  position: absolute;
  top: 45px;
  left: 50%;
  transform: translateX(-50%);
}

.drop {
  position: absolute;
  width: 2px;
  height: 15px;
  background: var(--luxury-cyan);
  border-radius: 0 0 50% 50%;
  animation: rain 1s linear infinite;
}

.drop-1 { left: -10px; animation-delay: 0s; }
.drop-2 { left: 0; animation-delay: 0.3s; }
.drop-3 { left: 10px; animation-delay: 0.6s; }

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes rain {
  0% { transform: translateY(-10px); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateY(20px); opacity: 0; }
}

/* Search Loader */
.search-loader {
  position: relative;
  width: 80px;
  height: 80px;
}

.search-rings {
  position: relative;
  width: 100%;
  height: 100%;
}

.ring {
  position: absolute;
  border: 2px solid transparent;
  border-radius: 50%;
  animation: searchPulse 2s ease-in-out infinite;
}

.ring-1 {
  width: 80px;
  height: 80px;
  border-top: 2px solid var(--luxury-purple);
  animation-delay: 0s;
}

.ring-2 {
  width: 60px;
  height: 60px;
  top: 10px;
  left: 10px;
  border-top: 2px solid var(--luxury-cyan);
  animation-delay: 0.5s;
}

.ring-3 {
  width: 40px;
  height: 40px;
  top: 20px;
  left: 20px;
  border-top: 2px solid var(--luxury-gold);
  animation-delay: 1s;
}

.search-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  background: var(--gradient-luxury);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: searchCore 1.5s ease-in-out infinite;
}

@keyframes searchPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.5; }
}

@keyframes searchCore {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.5); }
}

/* Data Loader */
.data-loader {
  display: flex;
  align-items: end;
  gap: 8px;
  height: 60px;
}

.bar {
  width: 8px;
  background: var(--gradient-luxury);
  border-radius: 4px 4px 0 0;
  animation: dataBar 1.5s ease-in-out infinite;
}

.bar-1 { height: 20px; animation-delay: 0s; }
.bar-2 { height: 35px; animation-delay: 0.2s; }
.bar-3 { height: 50px; animation-delay: 0.4s; }
.bar-4 { height: 30px; animation-delay: 0.6s; }
.bar-5 { height: 40px; animation-delay: 0.8s; }

@keyframes dataBar {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(1.5); }
}

/* Message Styling */
.loader-message {
  margin-top: 2rem;
  text-align: center;
}

.message-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  font-weight: 500;
  font-family: var(--font-primary);
}

.message-dots {
  display: inline-block;
  margin-left: 0.25rem;
}

.dot {
  animation: dots 1.5s infinite;
  color: rgba(255, 255, 255, 0.7);
}

.dot-1 { animation-delay: 0s; }
.dot-2 { animation-delay: 0.2s; }
.dot-3 { animation-delay: 0.4s; }

@keyframes dots {
  0%, 60%, 100% { opacity: 0; }
  30% { opacity: 1; }
}

/* Responsive */
@media (max-width: 768px) {
  .luxury-spinner {
    width: 60px;
    height: 60px;
  }
  
  .ring-outer { width: 60px; height: 60px; }
  .ring-middle { width: 45px; height: 45px; top: 7.5px; left: 7.5px; }
  .ring-inner { width: 30px; height: 30px; top: 15px; left: 15px; }
  .spinner-core { width: 15px; height: 15px; }
}
