import React, { useEffect, useState } from 'react';
import './PageTransition.css';

const PageTransition = ({ children, isLoading = false }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    if (!isLoading) {
      // Small delay to ensure smooth transition
      const timer = setTimeout(() => {
        setIsVisible(true);
        setShowContent(true);
      }, 100);

      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
      setShowContent(false);
    }
  }, [isLoading]);

  return (
    <div className="page-transition">
      {/* Loading overlay */}
      {isLoading && (
        <div className="transition-overlay">
          <div className="transition-loader">
            <div className="loader-rings">
              <div className="ring ring-1"></div>
              <div className="ring ring-2"></div>
              <div className="ring ring-3"></div>
            </div>
            <div className="loader-text">Loading...</div>
          </div>
        </div>
      )}

      {/* Content with transition */}
      <div className={`transition-content ${isVisible ? 'visible' : ''}`}>
        {showContent && children}
      </div>

      {/* Decorative elements */}
      <div className="transition-particles">
        {Array.from({ length: 20 }).map((_, index) => (
          <div
            key={index}
            className="particle"
            style={{
              '--delay': `${index * 0.1}s`,
              '--x': `${Math.random() * 100}%`,
              '--y': `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default PageTransition;
