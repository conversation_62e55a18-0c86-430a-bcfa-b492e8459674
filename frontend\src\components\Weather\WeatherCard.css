.weather-card {
  background: var(--white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--space-8);
  transition: all var(--transition-normal);
  max-width: 600px;
  margin: 0 auto;
  position: relative;
  color: var(--text-primary);
}

.weather-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: var(--border-medium);
}

.weather-card.compact {
  padding: var(--space-6);
  max-width: 380px;
  border-radius: var(--radius-lg);
}

.weather-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
}

.location-info {
  flex: 1;
}

.city-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.compact .city-name {
  font-size: 1.25rem;
  margin-bottom: var(--space-1);
}

.location-icon {
  color: var(--primary);
  font-size: 1.25rem;
}

.country-name {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--space-1);
  font-weight: 500;
}

.coordinates {
  color: var(--text-tertiary);
  font-size: 0.75rem;
  font-family: var(--font-mono);
  font-weight: 400;
}

.favorite-btn {
  background: var(--white);
  border: 1px solid var(--border-light);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-tertiary);
  font-size: 1.125rem;
  box-shadow: var(--shadow-sm);
}

.favorite-btn:hover {
  border-color: var(--error);
  color: var(--error);
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.favorite-btn.active {
  background: var(--error);
  border-color: var(--error);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.favorite-btn.guest {
  background: var(--gray-50);
  border-color: var(--border-medium);
  color: var(--text-tertiary);
  cursor: pointer;
}

.favorite-btn.guest:hover {
  border-color: var(--primary);
  color: var(--primary);
  background: var(--white);
  box-shadow: var(--shadow-md);
}

.weather-main {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.weather-main.compact {
  margin-bottom: var(--space-4);
  gap: var(--space-4);
}

.weather-icon-container {
  flex-shrink: 0;
}

.weather-icon {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

.weather-icon.large {
  width: 80px;
  height: 80px;
}

.weather-icon-temp {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.temperature-info {
  flex: 1;
}

.current-temp {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  display: block;
  line-height: 1;
}

.compact .current-temp {
  font-size: 1.875rem;
}

.feels-like {
  color: var(--text-tertiary);
  font-size: 0.875rem;
  display: block;
  margin-top: var(--space-1);
}

.weather-desc {
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  text-transform: capitalize;
  display: block;
  margin-top: var(--space-2);
}

.compact .weather-desc {
  font-size: 0.875rem;
  margin-top: var(--space-1);
}

.temp-range {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-3);
}

.temp-min,
.temp-max {
  color: var(--text-tertiary);
  font-size: 0.875rem;
  font-weight: 500;
}

.weather-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.weather-details.compact {
  flex-direction: row;
  gap: 1rem;
  justify-content: space-around;
}

.detail-row {
  display: flex;
  gap: 2rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.compact .detail-item {
  flex-direction: column;
  gap: 0.25rem;
  text-align: center;
}

.detail-icon {
  color: #667eea;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.compact .detail-icon {
  font-size: 1rem;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.detail-label {
  color: #718096;
  font-size: 0.85rem;
  font-weight: 500;
}

.detail-value {
  color: #2d3748;
  font-size: 0.95rem;
  font-weight: 600;
}

.compact .detail-value {
  font-size: 0.85rem;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.25rem;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer-progress 2s infinite;
}

@keyframes shimmer-progress {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.weather-footer {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.last-updated {
  color: #a0aec0;
  font-size: 0.8rem;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .weather-card {
    padding: 1.5rem;
  }
  
  .weather-main {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .weather-icon-temp {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .detail-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .city-name {
    font-size: 1.5rem;
  }
  
  .current-temp {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .weather-card {
    padding: 1rem;
  }
  
  .weather-header {
    margin-bottom: 1.5rem;
  }
  
  .city-name {
    font-size: 1.25rem;
  }
  
  .current-temp {
    font-size: 2rem;
  }
  
  .weather-icon.large {
    width: 100px;
    height: 100px;
  }
  
  .favorite-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .detail-item {
    padding: 0.75rem;
    background: #f7fafc;
    border-radius: 8px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .weather-card {
    background: #2d3748;
    color: white;
  }
  
  .city-name {
    color: #e2e8f0;
  }
  
  .country-name {
    color: #a0aec0;
  }
  
  .coordinates {
    color: #718096;
  }
  
  .current-temp {
    color: #e2e8f0;
  }
  
  .feels-like {
    color: #a0aec0;
  }
  
  .weather-desc {
    color: #cbd5e0;
  }
  
  .temp-min,
  .temp-max {
    color: #a0aec0;
  }
  
  .detail-label {
    color: #a0aec0;
  }
  
  .detail-value {
    color: #e2e8f0;
  }
  
  .weather-footer {
    border-top-color: #4a5568;
  }
  
  .last-updated {
    color: #718096;
  }
  
  .favorite-btn {
    border-color: #4a5568;
    color: #a0aec0;
  }
  
  .favorite-btn:hover {
    border-color: #ff6b6b;
    color: #ff6b6b;
  }
  
  .detail-item {
    background: #4a5568;
  }
}
