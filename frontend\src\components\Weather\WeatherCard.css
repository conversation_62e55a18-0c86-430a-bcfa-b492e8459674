.weather-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--space-8);
  transition: all var(--transition-normal);
  max-width: 600px;
  margin: 0 auto;
  position: relative;
  color: var(--text-primary);
}

.weather-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: var(--border-medium);
}

.weather-card.compact {
  padding: var(--space-6);
  max-width: 380px;
  border-radius: var(--radius-lg);
}

.weather-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
}

.location-info {
  flex: 1;
}

.city-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.compact .city-name {
  font-size: 1.25rem;
  margin-bottom: var(--space-1);
}

.location-icon {
  color: var(--primary);
  font-size: 1.25rem;
}

.country-name {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--space-1);
  font-weight: 500;
}

.coordinates {
  color: var(--text-tertiary);
  font-size: 0.75rem;
  font-family: var(--font-mono);
  font-weight: 400;
}

.favorite-btn {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-tertiary);
  font-size: 1.125rem;
  box-shadow: var(--shadow-sm);
}

.favorite-btn:hover {
  border-color: var(--error);
  color: var(--error);
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.favorite-btn.active {
  background: var(--error);
  border-color: var(--error);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.favorite-btn.guest {
  background: var(--gray-50);
  border-color: var(--border-medium);
  color: var(--text-tertiary);
  cursor: pointer;
}

.favorite-btn.guest:hover {
  border-color: var(--primary);
  color: var(--primary);
  background: var(--white);
  box-shadow: var(--shadow-md);
}

.weather-main {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.weather-main.compact {
  margin-bottom: var(--space-4);
  gap: var(--space-4);
}

.weather-icon-container {
  flex-shrink: 0;
}

.weather-icon {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

.weather-icon.large {
  width: 80px;
  height: 80px;
}

.weather-icon-temp {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.temperature-info {
  flex: 1;
}

.current-temp {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  display: block;
  line-height: 1;
}

.compact .current-temp {
  font-size: 1.875rem;
}

.feels-like {
  color: var(--text-tertiary);
  font-size: 0.875rem;
  display: block;
  margin-top: var(--space-1);
}

.weather-desc {
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  text-transform: capitalize;
  display: block;
  margin-top: var(--space-2);
}

.compact .weather-desc {
  font-size: 0.875rem;
  margin-top: var(--space-1);
}

.temp-range {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-3);
}

.temp-min,
.temp-max {
  color: var(--text-tertiary);
  font-size: 0.875rem;
  font-weight: 500;
}

.weather-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.weather-details.compact {
  flex-direction: row;
  gap: var(--space-3);
  justify-content: space-around;
}

.detail-row {
  display: flex;
  gap: var(--space-6);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
}

.compact .detail-item {
  flex-direction: column;
  gap: var(--space-1);
  text-align: center;
}

.detail-icon {
  color: var(--primary);
  font-size: 1.125rem;
  flex-shrink: 0;
}

.compact .detail-icon {
  font-size: 1rem;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.detail-label {
  color: var(--text-tertiary);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.detail-value {
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 600;
}

.compact .detail-value {
  font-size: 0.75rem;
}

.progress-bar {
  width: 100%;
  height: 3px;
  background: var(--gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-top: var(--space-1);
}

.progress-fill {
  height: 100%;
  border-radius: var(--radius-sm);
  transition: width 0.6s ease-out;
  position: relative;
}

.weather-footer {
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-light);
}

.last-updated {
  color: var(--text-tertiary);
  font-size: 0.75rem;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .weather-card {
    padding: var(--space-6);
  }

  .weather-main {
    flex-direction: column;
    text-align: center;
    gap: var(--space-4);
  }

  .weather-icon-temp {
    flex-direction: column;
    gap: var(--space-2);
  }

  .detail-row {
    flex-direction: column;
    gap: var(--space-4);
  }

  .city-name {
    font-size: 1.25rem;
  }

  .current-temp {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .weather-card {
    padding: var(--space-4);
  }

  .weather-header {
    margin-bottom: var(--space-4);
  }

  .city-name {
    font-size: 1.125rem;
  }

  .current-temp {
    font-size: 1.75rem;
  }

  .weather-icon.large {
    width: 64px;
    height: 64px;
  }

  .favorite-btn {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }
}

/* Dark mode specific overrides */
.dark .weather-card {
  background: var(--bg-primary);
  border-color: var(--border-light);
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

.dark .weather-card:hover {
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
}
