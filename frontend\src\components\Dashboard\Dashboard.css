.dashboard {
  min-height: 100vh;
  background: var(--bg-primary);
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-8) var(--space-4);
}

/* Hero Section */
.dashboard-hero {
  text-align: center;
  padding: var(--space-16) 0 var(--space-12);
}

.hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.hero-title {
  font-size: clamp(2rem, 4vw, 2.5rem);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--space-4);
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

.hero-subtitle {
  font-size: clamp(1rem, 2vw, 1.125rem);
  color: var(--text-secondary);
  max-width: 500px;
  margin: 0 auto var(--space-8);
  line-height: 1.6;
  font-weight: 400;
}

/* Section Styling */
.search-section {
  margin-bottom: var(--space-12);
}

.search-container {
  max-width: 600px;
  margin: 0 auto;
}

.current-weather-section,
.favorites-section {
  margin-bottom: var(--space-12);
}

.guest-section {
  margin-bottom: var(--space-10);
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.section-title {
  font-size: clamp(1.5rem, 3vw, 1.875rem);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  letter-spacing: -0.025em;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
}

.title-icon {
  font-size: 1.25em;
  color: var(--primary);
}

/* Container Styling */
.weather-card-container,
.favorites-container,
.guest-container {
  position: relative;
}

.weather-card-container {
  max-width: 800px;
  margin: 0 auto;
}

.favorites-container {
  max-width: 1000px;
  margin: 0 auto;
}

.guest-container {
  max-width: 500px;
  margin: 0 auto;
}

/* Responsive design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--space-6) var(--space-3);
  }

  .dashboard-hero {
    padding: var(--space-12) 0 var(--space-8);
  }

  .section-title {
    font-size: 1.25rem;
    margin-bottom: var(--space-4);
  }

  .search-section,
  .current-weather-section,
  .favorites-section {
    margin-bottom: var(--space-8);
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: var(--space-4) var(--space-3);
  }

  .dashboard-hero {
    padding: var(--space-8) 0 var(--space-6);
  }

  .section-title {
    font-size: 1.125rem;
  }
}

/* Dark mode specific overrides */
.dark .dashboard {
  background: var(--bg-primary);
}
