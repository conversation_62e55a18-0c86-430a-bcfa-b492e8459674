.dashboard {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.dashboard-container {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Hero Section */
.dashboard-hero {
  text-align: center;
  padding: 4rem 0 6rem;
  position: relative;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-badge {
  display: inline-block;
  position: relative;
  margin-bottom: 2rem;
  padding: 0.75rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  overflow: hidden;
}

.badge-text {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  position: relative;
  z-index: 2;
}

.badge-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.hero-title {
  font-family: var(--font-display);
  font-size: clamp(2.5rem, 6vw, 5rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: white;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
}

.highlight {
  background: var(--gradient-luxury);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--gradient-luxury);
  border-radius: 2px;
  opacity: 0.7;
}

.hero-subtitle {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin: 0 auto 3rem;
  line-height: 1.6;
  font-weight: 400;
}

.hero-cta {
  margin-top: 3rem;
}

.cta-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  padding: 2rem 3rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  max-width: 500px;
  margin: 0 auto;
  box-shadow: var(--shadow-glass);
  transition: all var(--animation-normal);
}

.cta-content:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-luxury);
  background: rgba(255, 255, 255, 0.15);
}

.cta-icon {
  font-size: 2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.cta-text h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.cta-text p {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* Section Styling */
.search-section {
  margin-bottom: 5rem;
}

.search-container {
  max-width: 700px;
  margin: 0 auto;
}

.current-weather-section,
.favorites-section {
  margin-bottom: 5rem;
}

.guest-section {
  margin-bottom: 4rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.section-title {
  font-family: var(--font-display);
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.01em;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.title-icon {
  font-size: 1.5em;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.section-decoration {
  width: 100px;
  height: 4px;
  background: var(--gradient-luxury);
  border-radius: 2px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.section-decoration::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

/* Container Styling */
.weather-card-container,
.favorites-container,
.guest-container {
  position: relative;
}

.weather-card-container {
  max-width: 800px;
  margin: 0 auto;
}

.favorites-container {
  max-width: 1200px;
  margin: 0 auto;
}

.guest-container {
  max-width: 600px;
  margin: 0 auto;
}

/* Responsive design */
@media (max-width: 768px) {
  .dashboard {
    padding: 1.5rem 0;
  }
  
  .dashboard-container {
    padding: 0 0.5rem;
  }
  
  .dashboard-header {
    margin-bottom: 2rem;
  }
  
  .dashboard-title {
    font-size: 2rem;
  }
  
  .dashboard-subtitle {
    font-size: 1rem;
  }
  
  .section-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .search-section,
  .current-weather-section,
  .favorites-section {
    margin-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .dashboard {
    padding: 1rem 0;
  }
  
  .dashboard-title {
    font-size: 1.75rem;
  }
  
  .dashboard-subtitle {
    font-size: 0.95rem;
  }
  
  .section-title {
    font-size: 1.25rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dashboard {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
  
  .dashboard-title {
    color: white;
  }
  
  .dashboard-subtitle {
    color: #a0aec0;
  }
  
  .section-title {
    color: #e2e8f0;
  }
}
