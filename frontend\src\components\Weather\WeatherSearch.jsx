import React, { useState, useRef, useEffect } from 'react';
import { FiSearch, FiMapPin } from 'react-icons/fi';
import { weatherAPI } from '../../services/api';
import LoadingSpinner from '../UI/LoadingSpinner';
import PremiumLoader from '../UI/PremiumLoader';
import toast from 'react-hot-toast';
import './WeatherSearch.css';

const WeatherSearch = ({ onSearch, loading }) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const searchRef = useRef(null);
  const debounceRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const searchCities = async (searchQuery) => {
    if (searchQuery.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      setSearchLoading(true);
      const response = await weatherAPI.searchCities(searchQuery);
      setSuggestions(response.data.data);
    } catch (error) {
      console.error('Error searching cities:', error);
      setSuggestions([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    setShowSuggestions(true);

    // Debounce the search
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      searchCities(value);
    }, 300);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (query.trim()) {
      onSearch(query.trim());
      setShowSuggestions(false);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    const cityName = suggestion.state 
      ? `${suggestion.name}, ${suggestion.state}, ${suggestion.country}`
      : `${suggestion.name}, ${suggestion.country}`;
    
    setQuery(cityName);
    onSearch(cityName);
    setShowSuggestions(false);
  };

  const handleGetCurrentLocation = () => {
    if (!navigator.geolocation) {
      toast.error('Geolocation is not supported by this browser');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;
          const response = await weatherAPI.getWeatherByCoordinates(latitude, longitude);
          const weatherData = response.data.data;
          setQuery(`${weatherData.city}, ${weatherData.country}`);
          onSearch(`${weatherData.city}, ${weatherData.country}`);
          toast.success('Current location weather loaded!');
        } catch (error) {
          console.error('Error getting current location weather:', error);
          toast.error('Failed to get weather for current location');
        }
      },
      (error) => {
        console.error('Geolocation error:', error);
        toast.error('Failed to get your location');
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  return (
    <div className="weather-search" ref={searchRef}>
      <form onSubmit={handleSubmit} className="search-form">
        <div className="search-input-container">
          <FiSearch className="search-icon" />
          <input
            type="text"
            value={query}
            onChange={handleInputChange}
            placeholder="Search for a city..."
            className="search-input"
            disabled={loading}
          />
          <button
            type="button"
            onClick={handleGetCurrentLocation}
            className="location-btn"
            title="Use current location"
            disabled={loading}
          >
            <FiMapPin />
          </button>
          <button
            type="submit"
            className="search-btn"
            disabled={loading || !query.trim()}
          >
            {loading ? <PremiumLoader size="small" type="search" message="" /> : 'Search'}
          </button>
        </div>
      </form>

      {showSuggestions && (suggestions.length > 0 || searchLoading) && (
        <div className="suggestions-container">
          {searchLoading ? (
            <div className="suggestion-item loading">
              <PremiumLoader size="small" type="search" message="Searching cities..." />
            </div>
          ) : (
            suggestions.map((suggestion, index) => (
              <div
                key={index}
                className="suggestion-item"
                onClick={() => handleSuggestionClick(suggestion)}
              >
                <FiMapPin className="suggestion-icon" />
                <div className="suggestion-content">
                  <span className="suggestion-name">{suggestion.name}</span>
                  <span className="suggestion-location">
                    {suggestion.state ? `${suggestion.state}, ` : ''}{suggestion.country}
                  </span>
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default WeatherSearch;
