.navbar {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-glass);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all var(--animation-normal);
}

.navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  position: relative;
}

.navbar-brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  font-family: var(--font-display);
  font-weight: 700;
  font-size: 1.5rem;
  transition: all var(--animation-normal);
  position: relative;
}

.navbar-brand:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.brand-icon {
  margin-right: 0.75rem;
  font-size: 2rem;
  color: var(--luxury-gold);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.brand-text {
  font-weight: 800;
  background: var(--gradient-luxury);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.navbar-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
}

.navbar-link:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.navbar-link.active {
  background: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

.link-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
}

.register-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.register-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.logout-btn {
  color: #ffcccb;
}

.logout-btn:hover {
  background: rgba(255, 204, 203, 0.2);
}

.navbar-mobile {
  display: none;
}

.mobile-menu-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.mobile-menu-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  text-align: left;
}

.mobile-link:hover {
  background: rgba(255, 255, 255, 0.1);
}

.mobile-link.active {
  background: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

.mobile-user-info {
  padding: 0.75rem 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 0.5rem;
}

.user-name {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .navbar-menu {
    display: none;
  }
  
  .navbar-mobile {
    display: block;
  }
  
  .navbar-container {
    padding: 0 1rem;
  }
  
  .brand-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    padding: 0 0.5rem;
  }
}
