.navbar {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all var(--transition-normal);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  position: relative;
}

.navbar-brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-weight: 600;
  font-size: 1.25rem;
  transition: color var(--transition-fast);
}

.navbar-brand:hover {
  color: var(--primary);
}

.brand-icon {
  margin-right: var(--space-2);
  font-size: 1.5rem;
  color: var(--primary);
}

.brand-text {
  font-weight: 600;
  color: var(--text-primary);
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.navbar-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
}

.navbar-link:hover {
  color: var(--text-primary);
  background: var(--gray-50);
}

.navbar-link.active {
  color: var(--primary);
  background: var(--gray-50);
  font-weight: 600;
}

.link-icon {
  margin-right: var(--space-2);
  font-size: 1rem;
}

.register-btn {
  background: var(--primary);
  color: var(--white);
  border: 1px solid var(--primary);
}

.register-btn:hover {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
  color: var(--white);
}





.logout-btn {
  color: var(--error);
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error);
}

.navbar-mobile {
  display: none;
}

.mobile-menu-toggle {
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: background var(--transition-fast);
}

.mobile-menu-toggle:hover {
  background: var(--gray-50);
}

.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-lg);
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.mobile-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  text-align: left;
}

.mobile-link:hover {
  background: var(--gray-50);
  color: var(--text-primary);
}

.mobile-link.active {
  background: var(--gray-50);
  color: var(--primary);
  font-weight: 600;
}

.mobile-user-info {
  padding: var(--space-3) var(--space-4);
  border-top: 1px solid var(--border-light);
  margin-top: var(--space-2);
}

.user-name {
  color: var(--text-tertiary);
  font-size: 0.75rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .navbar-menu {
    display: none;
  }

  .navbar-mobile {
    display: block;
  }

  .navbar-container {
    padding: 0 var(--space-4);
  }

  .brand-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    padding: 0 var(--space-3);
  }
}
