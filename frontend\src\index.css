/* Import Premium Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Premium Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-display: 'Playfair Display', serif;
  --font-mono: 'Space Grotesk', monospace;

  /* Luxury Color Palette */
  --luxury-gold: #D4AF37;
  --luxury-gold-light: #F7E98E;
  --luxury-gold-dark: #B8860B;

  --luxury-purple: #6366F1;
  --luxury-purple-light: #A5B4FC;
  --luxury-purple-dark: #4338CA;

  --luxury-cyan: #06B6D4;
  --luxury-cyan-light: #67E8F9;
  --luxury-cyan-dark: #0891B2;

  --luxury-rose: #F43F5E;
  --luxury-rose-light: #FDA4AF;
  --luxury-rose-dark: #E11D48;

  /* Premium Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-luxury: linear-gradient(135deg, #D4AF37 0%, #6366F1 50%, #06B6D4 100%);
  --gradient-aurora: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  --gradient-sunset: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  --gradient-ocean: linear-gradient(135deg, #667eea 0%, #06B6D4 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

  /* Advanced Shadows */
  --shadow-luxury: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 40px rgba(102, 126, 234, 0.3);
  --shadow-premium: 0 32px 64px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
  --shadow-glass: 0 8px 32px rgba(31, 38, 135, 0.37);

  /* Glass Effects */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(20px);

  /* Animation Variables */
  --animation-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --animation-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --animation-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --animation-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Typography */
  font-family: var(--font-primary);
  line-height: 1.6;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  min-height: 100vh;
  background: var(--gradient-aurora);
  color: #ffffff;
  font-size: 16px;
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
}

/* Animated Background */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.3) 0%, transparent 50%);
  animation: backgroundShift 20s ease-in-out infinite;
  z-index: -2;
}

body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  z-index: -1;
}

@keyframes backgroundShift {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: hue-rotate(0deg);
  }
  25% {
    transform: scale(1.1) rotate(90deg);
    filter: hue-rotate(90deg);
  }
  50% {
    transform: scale(1.05) rotate(180deg);
    filter: hue-rotate(180deg);
  }
  75% {
    transform: scale(1.1) rotate(270deg);
    filter: hue-rotate(270deg);
  }
}

/* Premium Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  background: var(--gradient-luxury);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
}
h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
}
h3 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  font-weight: 600;
}
h4 {
  font-size: clamp(1.25rem, 2.5vw, 1.875rem);
  font-weight: 600;
}
h5 {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  font-weight: 500;
}
h6 {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  font-weight: 500;
}

p {
  margin-bottom: 1rem;
}

/* Links */
a {
  color: var(--primary-600);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-700);
}

/* Form elements */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

button {
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
  border: none;
  background: none;
  transition: all 0.2s ease;
}

button:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Selection */
::selection {
  background: var(--primary-100);
  color: var(--primary-700);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Responsive images */
img {
  max-width: 100%;
  height: auto;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1a202c;
    color: #e2e8f0;
  }

  ::-webkit-scrollbar-track {
    background: #2d3748;
  }

  ::-webkit-scrollbar-thumb {
    background: #4a5568;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #718096;
  }

  ::selection {
    background: var(--primary-700);
    color: white;
  }
}
