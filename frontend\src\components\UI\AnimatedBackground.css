.animated-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -10;
  overflow: hidden;
}

.particle-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* Floating Geometric Shapes */
.floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
  animation: float 20s infinite ease-in-out;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 25s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: 15%;
  animation-delay: -5s;
  animation-duration: 30s;
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
}

.shape-3 {
  width: 150px;
  height: 150px;
  top: 30%;
  right: 30%;
  animation-delay: -10s;
  animation-duration: 20s;
  border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
}

.shape-4 {
  width: 250px;
  height: 250px;
  bottom: 20%;
  left: 20%;
  animation-delay: -15s;
  animation-duration: 35s;
  border-radius: 40% 60% 60% 40% / 60% 30% 70% 40%;
}

.shape-5 {
  width: 100px;
  height: 100px;
  top: 80%;
  left: 60%;
  animation-delay: -20s;
  animation-duration: 15s;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  25% {
    transform: translate(30px, -30px) rotate(90deg) scale(1.1);
  }
  50% {
    transform: translate(-20px, 20px) rotate(180deg) scale(0.9);
  }
  75% {
    transform: translate(-30px, -10px) rotate(270deg) scale(1.05);
  }
}

/* Aurora Effects */
.aurora {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  mix-blend-mode: screen;
  animation: aurora 15s infinite ease-in-out;
}

.aurora-1 {
  background: radial-gradient(ellipse at top, #667eea 0%, transparent 50%);
  animation-delay: 0s;
}

.aurora-2 {
  background: radial-gradient(ellipse at bottom left, #764ba2 0%, transparent 50%);
  animation-delay: -5s;
}

.aurora-3 {
  background: radial-gradient(ellipse at bottom right, #f093fb 0%, transparent 50%);
  animation-delay: -10s;
}

@keyframes aurora {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1) rotate(0deg);
  }
  25% {
    opacity: 0.5;
    transform: scale(1.1) rotate(90deg);
  }
  50% {
    opacity: 0.4;
    transform: scale(0.9) rotate(180deg);
  }
  75% {
    opacity: 0.6;
    transform: scale(1.05) rotate(270deg);
  }
}

/* Gradient Overlay */
.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.1) 0%,
    rgba(118, 75, 162, 0.1) 25%,
    rgba(240, 147, 251, 0.1) 50%,
    rgba(245, 87, 108, 0.1) 75%,
    rgba(79, 172, 254, 0.1) 100%
  );
  z-index: 3;
  animation: gradientShift 20s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    opacity: 0.1;
    filter: hue-rotate(0deg);
  }
  25% {
    opacity: 0.2;
    filter: hue-rotate(90deg);
  }
  50% {
    opacity: 0.15;
    filter: hue-rotate(180deg);
  }
  75% {
    opacity: 0.25;
    filter: hue-rotate(270deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .shape {
    transform: scale(0.7);
  }
  
  .shape-1 { width: 200px; height: 200px; }
  .shape-2 { width: 150px; height: 150px; }
  .shape-3 { width: 100px; height: 100px; }
  .shape-4 { width: 180px; height: 180px; }
  .shape-5 { width: 80px; height: 80px; }
}

@media (max-width: 480px) {
  .shape {
    transform: scale(0.5);
  }
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
  .shape,
  .aurora,
  .gradient-overlay {
    animation: none;
  }
  
  .particle-canvas {
    display: none;
  }
}
