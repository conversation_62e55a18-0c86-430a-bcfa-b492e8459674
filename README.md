# Kritka Weather App

A full-stack weather application built with the MERN stack (MongoDB, Express.js, React, Node.js) featuring user authentication, weather data from OpenWeatherMap API, and favorite cities management.

## 🌟 Features

- **User Authentication**: Secure registration and login with JWT tokens
- **Weather Search**: Search for cities and view current weather conditions
- **Favorite Cities**: Save, view, and manage your favorite cities
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Real-time Data**: Current weather including temperature, humidity, wind speed, and more
- **Geolocation**: Get weather for your current location
- **User Profile**: Manage your account information

## 🛠️ Tech Stack

### Frontend
- **React 19** with Vite for fast development
- **React Router** for navigation
- **React Icons** for beautiful icons
- **React Hot Toast** for notifications
- **Axios** for API calls
- **CSS3** with modern styling and animations

### Backend
- **Node.js** with Express.js framework
- **MongoDB** with Mongoose ODM
- **JWT** for authentication
- **bcryptjs** for password hashing
- **Express Validator** for input validation
- **Helmet** for security headers
- **CORS** for cross-origin requests
- **Rate Limiting** for API protection

### External APIs
- **OpenWeatherMap API** for weather data
- **OpenWeatherMap Geocoding API** for city search

## 📋 Prerequisites

Before running this application, make sure you have the following installed:

- **Node.js** (v16 or higher)
- **npm** or **yarn**
- **MongoDB** (local installation or MongoDB Atlas)
- **OpenWeatherMap API Key** (free at [openweathermap.org](https://openweathermap.org/api))

## 🚀 Installation & Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd kritka-weather-app
```

### 2. Install Dependencies

Install dependencies for both frontend and backend:

```bash
# Install root dependencies (for concurrent running)
npm install

# Install backend dependencies
npm run install-server

# Install frontend dependencies
npm run install-client
```

### 3. Environment Configuration

#### Backend Environment Variables

Create a `.env` file in the `backend` directory:

```bash
cd backend
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/kritka-weather-app

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRE=7d

# Weather API Configuration
WEATHER_API_KEY=your_openweathermap_api_key_here
WEATHER_API_URL=https://api.openweathermap.org/data/2.5

# CORS Configuration
CLIENT_URL=http://localhost:5173
```

#### Frontend Environment Variables

Create a `.env` file in the `frontend` directory:

```bash
cd frontend
cp .env.example .env
```

Edit the `.env` file:

```env
# API Configuration
VITE_API_URL=http://localhost:5000/api
VITE_APP_NAME=Kritka Weather App
```

### 4. Get OpenWeatherMap API Key

1. Visit [OpenWeatherMap](https://openweathermap.org/api)
2. Sign up for a free account
3. Go to API Keys section
4. Copy your API key
5. Add it to your backend `.env` file as `WEATHER_API_KEY`

### 5. Setup MongoDB

#### Option A: Local MongoDB
1. Install MongoDB locally
2. Start MongoDB service
3. Use the default connection string: `mongodb://localhost:27017/kritka-weather-app`

#### Option B: MongoDB Atlas (Cloud)
1. Create a free account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Get your connection string
4. Replace `MONGODB_URI` in your `.env` file

## 🏃‍♂️ Running the Application

### Development Mode

Run both frontend and backend concurrently:

```bash
npm run dev
```

This will start:
- Backend server on `http://localhost:5000`
- Frontend development server on `http://localhost:5173`

### Individual Services

Run backend only:
```bash
npm run server
```

Run frontend only:
```bash
npm run client
```

### Production Build

Build the frontend for production:
```bash
npm run build
```

Start the production server:
```bash
npm start
```

## 📱 Usage

1. **Register/Login**: Create an account or sign in with existing credentials
2. **Search Weather**: Use the search bar to find weather for any city
3. **Current Location**: Click the location icon to get weather for your current location
4. **Add Favorites**: Click the heart icon on any weather card to save it to favorites
5. **Manage Profile**: Update your name and email in the profile section
6. **View Favorites**: See all your favorite cities' weather on the dashboard

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update user profile

### Weather
- `GET /api/weather/current/:city` - Get weather by city name
- `GET /api/weather/coordinates?lat=&lon=` - Get weather by coordinates
- `GET /api/weather/search/:query` - Search cities

### Favorites
- `GET /api/favorites` - Get user's favorite cities
- `POST /api/favorites` - Add city to favorites
- `DELETE /api/favorites/:id` - Remove city from favorites
- `PUT /api/favorites/:id` - Update favorite city
- `GET /api/favorites/:id` - Get single favorite city

## 🎨 Features in Detail

### Weather Information Displayed
- Current temperature and "feels like" temperature
- Weather description and icon
- Humidity percentage
- Wind speed and direction
- Atmospheric pressure
- Visibility
- Sunrise and sunset times
- Min/max temperatures

### Security Features
- Password hashing with bcrypt
- JWT token authentication
- Input validation and sanitization
- Rate limiting
- CORS protection
- Security headers with Helmet
- Protected routes

### User Experience
- Responsive design for all screen sizes
- Loading states and error handling
- Toast notifications for user feedback
- Smooth animations and transitions
- Intuitive navigation
- Search suggestions with debouncing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [OpenWeatherMap](https://openweathermap.org/) for providing the weather API
- [React Icons](https://react-icons.github.io/react-icons/) for the beautiful icons
- [MongoDB](https://www.mongodb.com/) for the database solution

## 📞 Support

If you have any questions or run into issues, please open an issue on GitHub or contact the development team.

---

**Happy Weather Tracking! 🌤️**
