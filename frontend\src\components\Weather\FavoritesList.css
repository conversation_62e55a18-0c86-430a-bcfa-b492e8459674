.favorites-list {
  width: 100%;
}

.favorites-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: var(--space-6);
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--primary);
  color: var(--white);
  border: none;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
}

.refresh-btn:hover {
  background: var(--primary-dark);
  box-shadow: var(--shadow-md);
}

.refresh-btn svg {
  font-size: 1rem;
}

.favorites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6);
  justify-items: center;
}

.favorites-empty {
  text-align: center;
  padding: var(--space-12) var(--space-8);
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  max-width: 400px;
  margin: 0 auto;
  border: 1px solid var(--border-light);
}

.empty-icon {
  font-size: 4rem;
  color: #e2e8f0;
  margin-bottom: 1.5rem;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.empty-description {
  color: #718096;
  font-size: 1rem;
  line-height: 1.6;
  max-width: 400px;
  margin: 0 auto 2rem;
}

.empty-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-width: 300px;
  margin: 0 auto;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  color: #4a5568;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.feature-item span {
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .favorites-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .favorites-header {
    justify-content: center;
    margin-bottom: 1rem;
  }
  
  .refresh-btn {
    padding: 0.625rem 1rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .favorites-empty {
    padding: 3rem 1.5rem;
  }
  
  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }
  
  .empty-title {
    font-size: 1.25rem;
  }
  
  .empty-description {
    font-size: 0.9rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .favorites-empty {
    background: #2d3748;
    color: white;
  }
  
  .empty-icon {
    color: #4a5568;
  }
  
  .empty-title {
    color: #e2e8f0;
  }
  
  .empty-description {
    color: #a0aec0;
  }
}
