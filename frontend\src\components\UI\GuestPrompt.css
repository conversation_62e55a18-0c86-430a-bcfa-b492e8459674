.guest-prompt {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  text-align: center;
  color: var(--text-primary);
  box-shadow: var(--shadow-lg);
  max-width: 400px;
  margin: 0 auto;
}

.guest-prompt.compact {
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  gap: var(--space-6);
  text-align: left;
  max-width: 100%;
}

.prompt-header {
  margin-bottom: var(--space-6);
}

.prompt-icon {
  width: 64px;
  height: 64px;
  background: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-4);
  font-size: 1.5rem;
  color: var(--white);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.compact .prompt-icon {
  width: 60px;
  height: 60px;
  margin: 0;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.prompt-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.prompt-message {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.95;
  position: relative;
  z-index: 1;
}

.compact .prompt-message {
  font-size: 1rem;
  margin-bottom: 1rem;
}

.prompt-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 1;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.9;
}

.feature-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.feature-item span {
  font-size: 0.9rem;
  font-weight: 500;
}

.prompt-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.compact .prompt-actions {
  flex-direction: row;
  gap: 0.75rem;
  margin-bottom: 0;
}

.prompt-btn {
  padding: 0.875rem 2rem;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: inline-block;
  text-align: center;
  border: 2px solid transparent;
}

.compact .prompt-btn {
  padding: 0.625rem 1.25rem;
  font-size: 0.9rem;
  white-space: nowrap;
}

.prompt-btn.primary {
  background: var(--primary);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.prompt-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: var(--primary-dark);
}

.prompt-btn.secondary {
  background: transparent;
  color: var(--text-secondary);
  border-color: var(--border-medium);
}

.prompt-btn.secondary:hover {
  background: var(--gray-50);
  border-color: var(--border-dark);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.prompt-note {
  font-size: 0.85rem;
  opacity: 0.8;
  margin: 0;
  font-style: italic;
  position: relative;
  z-index: 1;
}

@keyframes float {
  0%, 100% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  50% {
    transform: translate(-50%, -50%) rotate(180deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .guest-prompt {
    padding: 2rem 1.5rem;
    margin: 0 1rem;
  }
  
  .prompt-features {
    flex-direction: column;
    gap: 1rem;
  }
  
  .feature-item {
    flex-direction: row;
    justify-content: center;
    gap: 1rem;
  }
  
  .prompt-title {
    font-size: 1.5rem;
  }
  
  .prompt-message {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .guest-prompt {
    padding: 1.5rem 1rem;
  }
  
  .compact {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .compact .prompt-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .compact .prompt-btn {
    width: 100%;
  }
  
  .prompt-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .prompt-title {
    font-size: 1.25rem;
  }
  
  .prompt-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
  }
}


