{"name": "kritka-weather-app", "version": "1.0.0", "description": "Full-stack weather application using MERN stack", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm run dev", "install-server": "cd backend && npm install", "install-client": "cd frontend && npm install", "install-all": "npm run install-server && npm run install-client", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "setup": "npm install && npm run install-all", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules", "reset": "npm run clean && npm run setup"}, "keywords": ["weather", "mern", "react", "nodejs", "mongodb"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}