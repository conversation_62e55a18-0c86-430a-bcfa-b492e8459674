.auth-container {
  min-height: calc(100vh - 64px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-4);
  background: var(--bg-secondary);
}

.auth-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-10);
  width: 100%;
  max-width: 400px;
  border: 1px solid var(--border-light);
}

.auth-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.auth-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.auth-subtitle {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: var(--space-3);
  color: var(--text-tertiary);
  font-size: 1rem;
  z-index: 1;
}

.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-3) var(--space-3) var(--space-10);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  background: var(--white);
  color: var(--text-primary);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input::placeholder {
  color: var(--text-tertiary);
}

.password-toggle {
  position: absolute;
  right: var(--space-3);
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  font-size: 1rem;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
}

.password-toggle:hover {
  color: var(--primary);
}

.auth-button {
  background: var(--primary);
  color: var(--white);
  border: none;
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-top: var(--space-2);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;
}

.auth-button:hover:not(:disabled) {
  background: var(--primary-dark);
  box-shadow: var(--shadow-md);
}

.auth-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--gray-400);
}

.auth-footer {
  text-align: center;
  margin-top: var(--space-8);
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.auth-link-text {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.auth-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 600;
  transition: color var(--transition-fast);
}

.auth-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.guest-btn {
  background: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
  width: 100%;
}

.guest-btn:hover {
  background: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

/* Responsive design */
@media (max-width: 480px) {
  .auth-container {
    padding: var(--space-4) var(--space-2);
  }

  .auth-card {
    padding: var(--space-8) var(--space-6);
    border-radius: var(--radius-lg);
  }

  .auth-title {
    font-size: 1.25rem;
  }

  .form-input {
    padding: var(--space-3) var(--space-3) var(--space-3) var(--space-8);
  }

  .auth-button {
    padding: var(--space-3) var(--space-4);
  }
}

/* Dark mode specific overrides */
.dark .auth-container {
  background: var(--bg-secondary);
}

.dark .auth-card {
  background: var(--bg-primary);
  border-color: var(--border-light);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
}
