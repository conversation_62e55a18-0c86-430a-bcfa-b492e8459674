.weather-search {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-form {
  width: 100%;
}

.search-input-container {
  display: flex;
  align-items: center;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--space-2);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-light);
  position: relative;
}

.search-input-container:focus-within {
  box-shadow: var(--shadow-xl);
  border-color: var(--primary);
}

.search-icon {
  color: var(--text-tertiary);
  font-size: 1.25rem;
  margin-left: var(--space-3);
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: var(--space-4) var(--space-3);
  font-size: 1rem;
  background: transparent;
  color: var(--text-primary);
  font-weight: 400;
  font-family: var(--font-primary);
}

.search-input::placeholder {
  color: var(--text-tertiary);
  font-weight: 400;
}

.location-btn {
  background: none;
  border: none;
  color: var(--text-tertiary);
  font-size: 1.125rem;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-right: var(--space-2);
}

.location-btn:hover:not(:disabled) {
  background: var(--gray-50);
  color: var(--primary);
}

.location-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.search-btn {
  background: var(--primary);
  color: var(--white);
  border: none;
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
  font-size: 0.875rem;
  box-shadow: var(--shadow-sm);
}

.search-btn:hover:not(:disabled) {
  background: var(--primary-dark);
  box-shadow: var(--shadow-md);
}

.search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--gray-400);
}

.suggestions-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  margin-top: var(--space-2);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  border: 1px solid var(--border-light);
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: var(--space-4);
  cursor: pointer;
  transition: background var(--transition-fast);
  border-bottom: 1px solid var(--border-light);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background: var(--gray-50);
}

.suggestion-item.loading {
  justify-content: center;
  cursor: default;
}

.suggestion-item.loading:hover {
  background: var(--white);
}

.suggestion-icon {
  color: var(--primary);
  font-size: 1rem;
  margin-right: var(--space-3);
  flex-shrink: 0;
}

.suggestion-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.suggestion-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.suggestion-location {
  color: var(--text-tertiary);
  font-size: 0.75rem;
  margin-top: var(--space-1);
}

/* Responsive design */
@media (max-width: 768px) {
  .search-input-container {
    padding: var(--space-1);
  }

  .search-input {
    padding: var(--space-3) var(--space-2);
    font-size: 0.875rem;
  }

  .search-btn {
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    min-width: 80px;
  }

  .location-btn {
    font-size: 1rem;
    padding: var(--space-2);
  }
}

@media (max-width: 480px) {
  .search-input-container {
    flex-direction: column;
    gap: var(--space-2);
    padding: var(--space-4);
  }

  .search-icon {
    display: none;
  }

  .search-input {
    padding: var(--space-3);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    background: var(--gray-50);
  }

  .search-input:focus {
    border-color: var(--primary);
    background: var(--white);
  }

  .location-btn {
    align-self: flex-start;
  }

  .search-btn {
    width: 100%;
    justify-content: center;
  }

  .suggestion-item {
    padding: var(--space-3);
  }
}

/* Dark mode specific overrides */
.dark .search-input-container {
  background: var(--bg-primary);
  border-color: var(--border-light);
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

.dark .search-input-container:focus-within {
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
}

.dark .suggestions-container {
  background: var(--bg-primary);
  border-color: var(--border-light);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
}
