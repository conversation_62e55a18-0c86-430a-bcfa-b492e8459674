.weather-search {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-form {
  width: 100%;
}

.search-input-container {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  border-radius: 28px;
  box-shadow: var(--shadow-premium);
  padding: 1rem;
  transition: all var(--animation-slow);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.search-input-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-luxury);
  border-radius: 28px 28px 0 0;
  opacity: 0;
  transition: all var(--animation-normal);
}

.search-input-container::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
  pointer-events: none;
}

.search-input-container:focus-within {
  box-shadow: var(--shadow-luxury), var(--shadow-glow);
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.15);
}

.search-input-container:focus-within::before {
  opacity: 1;
}

.search-icon {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
  margin-left: 1rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 1.25rem 1rem;
  font-size: 1.1rem;
  background: transparent;
  color: white;
  font-weight: 500;
  font-family: var(--font-primary);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
}

.location-btn {
  background: none;
  border: none;
  color: #667eea;
  font-size: 1.2rem;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 0.5rem;
}

.location-btn:hover:not(:disabled) {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(1.1);
}

.location-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 14px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
  font-size: 0.95rem;
  letter-spacing: 0.025em;
  box-shadow:
    0 4px 12px rgba(102, 126, 234, 0.3),
    0 1px 0px rgba(255, 255, 255, 0.2) inset;
  position: relative;
  overflow: hidden;
}

.search-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.search-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 1px 0px rgba(255, 255, 255, 0.3) inset;
}

.search-btn:hover:not(:disabled)::before {
  left: 100%;
}

.search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.suggestions-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  margin-top: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  border: 1px solid #e2e8f0;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  cursor: pointer;
  transition: background 0.2s ease;
  border-bottom: 1px solid #f7fafc;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background: #f7fafc;
}

.suggestion-item.loading {
  justify-content: center;
  cursor: default;
}

.suggestion-item.loading:hover {
  background: white;
}

.suggestion-icon {
  color: #667eea;
  font-size: 1rem;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.suggestion-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.suggestion-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.95rem;
}

.suggestion-location {
  color: #718096;
  font-size: 0.85rem;
  margin-top: 0.125rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-input-container {
    padding: 0.375rem;
  }
  
  .search-input {
    padding: 0.875rem 0.5rem;
    font-size: 0.95rem;
  }
  
  .search-btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.9rem;
    min-width: 70px;
  }
  
  .location-btn {
    font-size: 1.1rem;
    padding: 0.375rem;
  }
}

@media (max-width: 480px) {
  .search-input-container {
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
  }
  
  .search-icon {
    display: none;
  }
  
  .search-input {
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: #fafafa;
  }
  
  .search-input:focus {
    border-color: #667eea;
    background: white;
  }
  
  .location-btn {
    align-self: flex-start;
  }
  
  .search-btn {
    width: 100%;
    justify-content: center;
  }
  
  .suggestion-item {
    padding: 0.875rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .search-input-container {
    background: #2d3748;
    border: 1px solid #4a5568;
  }
  
  .search-input {
    color: white;
  }
  
  .search-input::placeholder {
    color: #718096;
  }
  
  .suggestions-container {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .suggestion-item {
    border-bottom-color: #4a5568;
  }
  
  .suggestion-item:hover {
    background: #4a5568;
  }
  
  .suggestion-name {
    color: #e2e8f0;
  }
  
  .suggestion-location {
    color: #a0aec0;
  }
}
