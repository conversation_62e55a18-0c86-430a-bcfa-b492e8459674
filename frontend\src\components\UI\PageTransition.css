.page-transition {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
}

.transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.transition-loader {
  text-align: center;
  color: white;
}

.loader-rings {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto 2rem;
}

.ring {
  position: absolute;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.ring-1 {
  width: 100px;
  height: 100px;
  border-top: 3px solid var(--luxury-gold);
  border-right: 3px solid var(--luxury-purple);
}

.ring-2 {
  width: 70px;
  height: 70px;
  top: 15px;
  left: 15px;
  border-top: 3px solid var(--luxury-cyan);
  border-left: 3px solid var(--luxury-rose);
  animation-duration: 1.5s;
  animation-direction: reverse;
}

.ring-3 {
  width: 40px;
  height: 40px;
  top: 30px;
  left: 30px;
  border-bottom: 3px solid var(--luxury-gold);
  border-right: 3px solid var(--luxury-purple);
  animation-duration: 1s;
}

.loader-text {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 600;
  background: var(--gradient-luxury);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: pulse 2s ease-in-out infinite;
}

.transition-content {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-content.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.transition-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--gradient-luxury);
  border-radius: 50%;
  left: var(--x);
  top: var(--y);
  animation: particleFloat 8s ease-in-out infinite;
  animation-delay: var(--delay);
  opacity: 0.6;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) rotate(270deg);
    opacity: 0.4;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .loader-rings {
    width: 80px;
    height: 80px;
  }
  
  .ring-1 {
    width: 80px;
    height: 80px;
  }
  
  .ring-2 {
    width: 56px;
    height: 56px;
    top: 12px;
    left: 12px;
  }
  
  .ring-3 {
    width: 32px;
    height: 32px;
    top: 24px;
    left: 24px;
  }
  
  .loader-text {
    font-size: 1.25rem;
  }
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
  .ring,
  .loader-text,
  .particle {
    animation: none;
  }
  
  .transition-content {
    transition: opacity 0.3s ease;
  }
  
  .transition-content.visible {
    transform: none;
  }
}
