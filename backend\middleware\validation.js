const { body, query, param } = require('express-validator');

// User registration validation
const validateRegister = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Name can only contain letters and spaces'),
  
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number')
];

// User login validation
const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

// Profile update validation
const validateProfileUpdate = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Name can only contain letters and spaces'),
  
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address')
];

// Add favorite city validation
const validateAddFavorite = [
  body('cityName')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('City name must be between 1 and 100 characters')
    .matches(/^[a-zA-Z0-9\s\-'\.(),]+$/)
    .withMessage('City name contains invalid characters'),

  body('country')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Country must be between 1 and 100 characters')
    .matches(/^[a-zA-Z0-9\s\-'\.(),]+$/)
    .withMessage('Country name contains invalid characters'),
  
  body('coordinates.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  
  body('coordinates.lon')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),
  
  body('timezone')
    .optional()
    .isString()
    .withMessage('Timezone must be a string')
];

// Update favorite city validation
const validateUpdateFavorite = [
  body('cityName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('City name must be between 1 and 100 characters')
    .matches(/^[a-zA-Z0-9\s\-'\.(),]+$/)
    .withMessage('City name contains invalid characters'),

  body('country')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Country must be between 1 and 100 characters')
    .matches(/^[a-zA-Z0-9\s\-'\.(),]+$/)
    .withMessage('Country name contains invalid characters'),
  
  body('coordinates.lat')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  
  body('coordinates.lon')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),
  
  body('timezone')
    .optional()
    .isString()
    .withMessage('Timezone must be a string')
];

// Weather by coordinates validation
const validateWeatherCoordinates = [
  query('lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  
  query('lon')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180')
];

// MongoDB ObjectId validation
const validateObjectId = [
  param('id')
    .isMongoId()
    .withMessage('Invalid ID format')
];

// City search validation
const validateCitySearch = [
  param('query')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search query must be between 2 and 100 characters')
    .matches(/^[a-zA-Z0-9\s\-'\.(),]+$/)
    .withMessage('Search query contains invalid characters')
];

// City name validation for weather endpoint
const validateCityName = [
  param('city')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('City name must be between 1 and 100 characters')
    .matches(/^[a-zA-Z0-9\s\-'\.(),]+$/)
    .withMessage('City name contains invalid characters')
];

module.exports = {
  validateRegister,
  validateLogin,
  validateProfileUpdate,
  validateAddFavorite,
  validateUpdateFavorite,
  validateWeatherCoordinates,
  validateObjectId,
  validateCitySearch,
  validateCityName
};
