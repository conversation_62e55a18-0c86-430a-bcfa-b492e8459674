import React from 'react';
import './PremiumLoader.css';

const PremiumLoader = ({ size = 'medium', message = 'Loading...', type = 'default' }) => {
  const renderLoader = () => {
    switch (type) {
      case 'weather':
        return (
          <div className="premium-loader weather-loader">
            <div className="weather-icon">
              <div className="cloud">
                <div className="cloud-part cloud-part-1"></div>
                <div className="cloud-part cloud-part-2"></div>
                <div className="cloud-part cloud-part-3"></div>
              </div>
              <div className="rain">
                <div className="drop drop-1"></div>
                <div className="drop drop-2"></div>
                <div className="drop drop-3"></div>
              </div>
            </div>
          </div>
        );
      
      case 'search':
        return (
          <div className="premium-loader search-loader">
            <div className="search-rings">
              <div className="ring ring-1"></div>
              <div className="ring ring-2"></div>
              <div className="ring ring-3"></div>
            </div>
            <div className="search-pulse"></div>
          </div>
        );
      
      case 'data':
        return (
          <div className="premium-loader data-loader">
            <div className="data-bars">
              <div className="bar bar-1"></div>
              <div className="bar bar-2"></div>
              <div className="bar bar-3"></div>
              <div className="bar bar-4"></div>
              <div className="bar bar-5"></div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="premium-loader default-loader">
            <div className="luxury-spinner">
              <div className="spinner-ring ring-outer"></div>
              <div className="spinner-ring ring-middle"></div>
              <div className="spinner-ring ring-inner"></div>
              <div className="spinner-core"></div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`premium-loader-container ${size}`}>
      {renderLoader()}
      {message && (
        <div className="loader-message">
          <span className="message-text">{message}</span>
          <div className="message-dots">
            <span className="dot dot-1">.</span>
            <span className="dot dot-2">.</span>
            <span className="dot dot-3">.</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default PremiumLoader;
