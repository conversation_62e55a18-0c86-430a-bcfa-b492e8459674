import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { FiCloud, FiUser, FiLogOut, FiMenu, FiX, FiSun, FiMoon } from 'react-icons/fi';
import './Navbar.css';

const Navbar = () => {
  const { isAuthenticated, isGuest, user, logout } = useAuth();
  const { isDarkMode, toggleTheme } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/login');
    setIsMobileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <nav className="navbar">
      <div className="navbar-container">
        <Link to="/" className="navbar-brand" onClick={closeMobileMenu}>
          <FiCloud className="brand-icon" />
          <span className="brand-text">WeatherApp</span>
        </Link>

        <div className="navbar-menu">
          {isAuthenticated ? (
            <>
              <Link
                to="/"
                className={`navbar-link ${isActive('/') || isActive('/dashboard') ? 'active' : ''}`}
              >
                Dashboard
              </Link>
              <Link
                to="/profile"
                className={`navbar-link ${isActive('/profile') ? 'active' : ''}`}
              >
                <FiUser className="link-icon" />
                Profile
              </Link>
              <button
                onClick={toggleTheme}
                className="navbar-link theme-toggle"
                title={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
              >
                {isDarkMode ? <FiSun className="link-icon" /> : <FiMoon className="link-icon" />}
              </button>
              <button
                onClick={handleLogout}
                className="navbar-link logout-btn"
              >
                <FiLogOut className="link-icon" />
                Logout
              </button>
            </>
          ) : (
            <>
              <Link
                to="/"
                className={`navbar-link ${isActive('/') || isActive('/dashboard') ? 'active' : ''}`}
              >
                Weather
              </Link>
              <button
                onClick={toggleTheme}
                className="navbar-link theme-toggle"
                title={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
              >
                {isDarkMode ? <FiSun className="link-icon" /> : <FiMoon className="link-icon" />}
              </button>
              <Link
                to="/login"
                className={`navbar-link ${isActive('/login') ? 'active' : ''}`}
              >
                Login
              </Link>
              <Link
                to="/register"
                className={`navbar-link register-btn ${isActive('/register') ? 'active' : ''}`}
              >
                Sign Up
              </Link>
            </>
          )}
        </div>

        <div className="navbar-mobile">
          <button
            className="mobile-menu-toggle"
            onClick={toggleMobileMenu}
            aria-label="Toggle mobile menu"
          >
            {isMobileMenuOpen ? <FiX /> : <FiMenu />}
          </button>
        </div>

        {isMobileMenuOpen && (
          <div className="mobile-menu">
            {isAuthenticated ? (
              <>
                <Link
                  to="/"
                  className={`mobile-link ${isActive('/') || isActive('/dashboard') ? 'active' : ''}`}
                  onClick={closeMobileMenu}
                >
                  Dashboard
                </Link>
                <Link
                  to="/profile"
                  className={`mobile-link ${isActive('/profile') ? 'active' : ''}`}
                  onClick={closeMobileMenu}
                >
                  <FiUser className="link-icon" />
                  Profile
                </Link>
                <button
                  onClick={toggleTheme}
                  className="mobile-link theme-toggle"
                >
                  {isDarkMode ? <FiSun className="link-icon" /> : <FiMoon className="link-icon" />}
                  {isDarkMode ? 'Light Mode' : 'Dark Mode'}
                </button>
                <button
                  onClick={handleLogout}
                  className="mobile-link logout-btn"
                >
                  <FiLogOut className="link-icon" />
                  Logout
                </button>
                <div className="mobile-user-info">
                  <span className="user-name">Welcome, {user?.name}</span>
                </div>
              </>
            ) : (
              <>
                <Link
                  to="/"
                  className={`mobile-link ${isActive('/') || isActive('/dashboard') ? 'active' : ''}`}
                  onClick={closeMobileMenu}
                >
                  Weather
                </Link>
                <button
                  onClick={toggleTheme}
                  className="mobile-link theme-toggle"
                >
                  {isDarkMode ? <FiSun className="link-icon" /> : <FiMoon className="link-icon" />}
                  {isDarkMode ? 'Light Mode' : 'Dark Mode'}
                </button>
                <Link
                  to="/login"
                  className={`mobile-link ${isActive('/login') ? 'active' : ''}`}
                  onClick={closeMobileMenu}
                >
                  Login
                </Link>
                <Link
                  to="/register"
                  className={`mobile-link ${isActive('/register') ? 'active' : ''}`}
                  onClick={closeMobileMenu}
                >
                  Sign Up
                </Link>
              </>
            )}
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
