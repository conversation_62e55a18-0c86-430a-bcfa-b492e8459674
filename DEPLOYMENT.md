# Deployment Guide

This guide covers deploying the Kritka Weather App to various platforms.

## 🚀 Deployment Options

### 1. Hero<PERSON> (Recommended for beginners)

#### Backend Deployment

1. **Prepare for Heroku**
   ```bash
   # Install Heroku CLI
   # Create Procfile in backend directory
   echo "web: node server.js" > backend/Procfile
   ```

2. **Deploy Backend**
   ```bash
   cd backend
   git init
   heroku create your-app-name-backend
   heroku config:set NODE_ENV=production
   heroku config:set MONGODB_URI=your_mongodb_atlas_connection_string
   heroku config:set JWT_SECRET=your_jwt_secret
   heroku config:set WEATHER_API_KEY=your_openweathermap_api_key
   heroku config:set WEATHER_API_URL=https://api.openweathermap.org/data/2.5
   heroku config:set CLIENT_URL=https://your-frontend-url.netlify.app
   git add .
   git commit -m "Deploy backend"
   git push heroku main
   ```

#### Frontend Deployment (Netlify)

1. **Build the frontend**
   ```bash
   cd frontend
   npm run build
   ```

2. **Deploy to Netlify**
   - Drag and drop the `dist` folder to Netlify
   - Or connect your GitHub repository to Netlify
   - Set environment variable: `VITE_API_URL=https://your-backend-app.herokuapp.com/api`

### 2. Vercel + MongoDB Atlas

#### Backend (Vercel)

1. **Create vercel.json in backend directory**
   ```json
   {
     "version": 2,
     "builds": [
       {
         "src": "server.js",
         "use": "@vercel/node"
       }
     ],
     "routes": [
       {
         "src": "/(.*)",
         "dest": "/server.js"
       }
     ]
   }
   ```

2. **Deploy**
   ```bash
   cd backend
   vercel --prod
   ```

#### Frontend (Vercel)

```bash
cd frontend
vercel --prod
```

### 3. DigitalOcean App Platform

1. **Create App**
   - Connect your GitHub repository
   - Configure build and run commands
   - Set environment variables

2. **Backend Configuration**
   - Build Command: `cd backend && npm install`
   - Run Command: `cd backend && npm start`

3. **Frontend Configuration**
   - Build Command: `cd frontend && npm run build`
   - Output Directory: `frontend/dist`

### 4. AWS (Advanced)

#### Using AWS Elastic Beanstalk

1. **Backend**
   - Create Elastic Beanstalk application
   - Upload backend code as ZIP
   - Configure environment variables

2. **Frontend**
   - Build the app: `npm run build`
   - Upload to S3 bucket
   - Configure CloudFront for CDN

## 🔧 Environment Variables for Production

### Backend (.env)
```env
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb+srv://username:<EMAIL>/kritka-weather-app
JWT_SECRET=your_super_long_random_jwt_secret_for_production
JWT_EXPIRE=7d
WEATHER_API_KEY=your_openweathermap_api_key
WEATHER_API_URL=https://api.openweathermap.org/data/2.5
CLIENT_URL=https://your-frontend-domain.com
```

### Frontend (.env)
```env
VITE_API_URL=https://your-backend-domain.com/api
VITE_APP_NAME=Kritka Weather App
```

## 📋 Pre-deployment Checklist

- [ ] MongoDB Atlas cluster created and configured
- [ ] OpenWeatherMap API key obtained
- [ ] Environment variables set for production
- [ ] CORS configured for production domains
- [ ] JWT secret is secure and random
- [ ] Database connection string is correct
- [ ] Frontend API URL points to production backend
- [ ] All dependencies are in package.json
- [ ] Build process works locally
- [ ] Error handling is implemented
- [ ] Security headers are configured

## 🔒 Security Considerations

1. **Environment Variables**
   - Never commit `.env` files
   - Use platform-specific environment variable settings
   - Rotate secrets regularly

2. **Database Security**
   - Use MongoDB Atlas with IP whitelisting
   - Enable authentication
   - Use strong passwords

3. **API Security**
   - Rate limiting is enabled
   - CORS is properly configured
   - Input validation is implemented
   - JWT secrets are secure

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check CLIENT_URL in backend environment
   - Verify frontend API URL

2. **Database Connection**
   - Verify MongoDB connection string
   - Check IP whitelist in MongoDB Atlas
   - Ensure database user has proper permissions

3. **API Key Issues**
   - Verify OpenWeatherMap API key is active
   - Check API usage limits

4. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Check for environment-specific code

### Logs and Debugging

- **Heroku**: `heroku logs --tail`
- **Vercel**: Check function logs in dashboard
- **Netlify**: Check deploy logs in dashboard

## 📊 Monitoring

Consider adding:
- Error tracking (Sentry)
- Performance monitoring
- Uptime monitoring
- Analytics

## 🔄 CI/CD Pipeline

Example GitHub Actions workflow:

```yaml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm run build
      - run: npm run test
      # Add deployment steps
```

---

For more detailed platform-specific instructions, refer to the respective platform documentation.
